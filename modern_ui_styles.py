# -*- coding: utf-8 -*-
"""
现代化UI样式定义
包含科技感浅色主题、Neumorphism效果、渐变与光效等现代设计元素
"""

class ModernUIStyles:
    """现代化UI样式类"""
    
    # 色彩方案
    COLORS = {
        'primary': '#667eea',      # 科技蓝
        'secondary': '#764ba2',    # 深紫
        'background': '#f8fafc',   # 浅灰白
        'surface': '#ffffff',      # 纯白
        'text_primary': '#1e293b', # 深灰
        'text_secondary': '#64748b', # 中灰
        'success': '#10b981',      # 翠绿
        'warning': '#f59e0b',      # 橙黄
        'error': '#ef4444',        # 红色
        'info': '#3b82f6',         # 蓝色
        'border': '#e2e8f0',       # 边框色
        'shadow': 'rgba(0, 0, 0, 0.1)', # 阴影色
    }
    
    @staticmethod
    def get_main_window_style():
        """获取主窗口样式"""
        return f"""
        QWidget {{
            background: {ModernUIStyles.COLORS['background']};
            color: {ModernUIStyles.COLORS['text_primary']};
            font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            font-size: 11pt;
        }}
        
        QMainWindow {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['background']} 0%, #e2e8f0 100%);
        }}
        """
    
    @staticmethod
    def get_neumorphism_card_style():
        """获取新拟态卡片样式"""
        return f"""
        .neumorphism-card {{
            background: {ModernUIStyles.COLORS['surface']};
            border-radius: 20px;
            border: 1px solid {ModernUIStyles.COLORS['border']};
            box-shadow: 
                8px 8px 16px rgba(163, 177, 198, 0.6),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
            padding: 20px;
            margin: 10px;
        }}
        
        .neumorphism-card:hover {{
            box-shadow: 
                12px 12px 24px rgba(163, 177, 198, 0.8),
                -12px -12px 24px rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}
        """
    
    @staticmethod
    def get_modern_button_style():
        """获取现代化按钮样式"""
        return f"""
        QPushButton {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 11pt;
            min-height: 20px;
            box-shadow: 
                0 4px 15px rgba(102, 126, 234, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }}
        
        QPushButton:hover {{
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            box-shadow: 
                0 8px 25px rgba(102, 126, 234, 0.6),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}
        
        QPushButton:pressed {{
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
            transform: translateY(0px);
            box-shadow: 
                0 2px 8px rgba(102, 126, 234, 0.3),
                inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }}
        
        QPushButton:disabled {{
            background: #9ca3af;
            color: #6b7280;
            box-shadow: none;
            transform: none;
        }}
        """
    
    @staticmethod
    def get_glass_morphism_style():
        """获取玻璃拟态样式"""
        return f"""
        .glass-morphism {{
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            border-radius: 16px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }}
        
        .glass-morphism:hover {{
            background: rgba(255, 255, 255, 0.35);
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.45);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}
        """
    
    @staticmethod
    def get_modern_tab_style():
        """获取现代化Tab样式"""
        return f"""
        QTabWidget::pane {{
            border: none;
            background: {ModernUIStyles.COLORS['surface']};
            border-radius: 16px;
            margin-top: 10px;
            box-shadow: 
                0 10px 30px rgba(0, 0, 0, 0.1),
                0 1px 8px rgba(0, 0, 0, 0.2);
        }}
        
        QTabBar::tab {{
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            color: {ModernUIStyles.COLORS['text_secondary']};
            padding: 12px 20px;
            margin-right: 4px;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            border: 1px solid {ModernUIStyles.COLORS['border']};
            border-bottom: none;
            font-weight: 600;
            min-width: 80px;
        }}
        
        QTabBar::tab:selected {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            color: white;
            border-color: {ModernUIStyles.COLORS['primary']};
            box-shadow: 
                0 -2px 10px rgba(102, 126, 234, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }}
        
        QTabBar::tab:hover:!selected {{
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            color: {ModernUIStyles.COLORS['text_primary']};
            transform: translateY(-1px);
            transition: all 0.2s ease;
        }}
        """
    
    @staticmethod
    def get_modern_input_style():
        """获取现代化输入框样式"""
        return f"""
        QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            background: {ModernUIStyles.COLORS['surface']};
            border: 2px solid {ModernUIStyles.COLORS['border']};
            border-radius: 10px;
            padding: 10px 15px;
            font-size: 11pt;
            color: {ModernUIStyles.COLORS['text_primary']};
            selection-background-color: {ModernUIStyles.COLORS['primary']};
        }}
        
        QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
            border-color: {ModernUIStyles.COLORS['primary']};
            box-shadow: 
                0 0 0 3px rgba(102, 126, 234, 0.1),
                0 2px 8px rgba(102, 126, 234, 0.15);
            background: {ModernUIStyles.COLORS['surface']};
        }}
        
        QLineEdit:hover, QSpinBox:hover, QDoubleSpinBox:hover, QComboBox:hover {{
            border-color: #cbd5e1;
            transition: border-color 0.2s ease;
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {ModernUIStyles.COLORS['text_secondary']};
            margin-right: 10px;
        }}
        """
    
    @staticmethod
    def get_modern_checkbox_style():
        """获取现代化复选框样式"""
        return f"""
        QCheckBox {{
            font-weight: 600;
            color: {ModernUIStyles.COLORS['text_primary']};
            spacing: 10px;
            padding: 5px;
        }}
        
        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border-radius: 6px;
            border: 2px solid {ModernUIStyles.COLORS['border']};
            background: {ModernUIStyles.COLORS['surface']};
        }}
        
        QCheckBox::indicator:hover {{
            border-color: {ModernUIStyles.COLORS['primary']};
            background: rgba(102, 126, 234, 0.05);
            transition: all 0.2s ease;
        }}
        
        QCheckBox::indicator:checked {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['success']} 0%, #059669 100%);
            border-color: {ModernUIStyles.COLORS['success']};
            image: none;
        }}
        
        QCheckBox::indicator:checked:after {{
            content: "✓";
            color: white;
            font-weight: bold;
            font-size: 12px;
        }}
        """
    
    @staticmethod
    def get_modern_slider_style():
        """获取现代化滑块样式"""
        return f"""
        QSlider::groove:horizontal {{
            border: none;
            height: 8px;
            background: linear-gradient(90deg, {ModernUIStyles.COLORS['border']} 0%, #cbd5e1 100%);
            border-radius: 4px;
        }}
        
        QSlider::handle:horizontal {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            border: 3px solid white;
            width: 20px;
            height: 20px;
            margin: -8px 0;
            border-radius: 13px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }}
        
        QSlider::handle:horizontal:hover {{
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.5);
            transform: scale(1.1);
            transition: all 0.2s ease;
        }}
        
        QSlider::sub-page:horizontal {{
            background: linear-gradient(90deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            border-radius: 4px;
        }}
        """
    
    @staticmethod
    def get_status_indicator_style():
        """获取状态指示器样式"""
        return f"""
        .status-connected {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['success']} 0%, #059669 100%);
            color: white;
            border-radius: 20px;
            padding: 8px 16px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }}
        
        .status-disconnected {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['error']} 0%, #dc2626 100%);
            color: white;
            border-radius: 20px;
            padding: 8px 16px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }}
        
        .status-indicator {{
            animation: pulse 2s infinite;
        }}
        
        @keyframes pulse {{
            0% {{ opacity: 1; }}
            50% {{ opacity: 0.7; }}
            100% {{ opacity: 1; }}
        }}
        """
