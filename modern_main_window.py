# -*- coding: utf-8 -*-
"""
现代化主窗口
基于原有功能，采用现代化UI设计
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QScrollArea, QFrame, QSplitter, QApplication
)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPixmap, QIcon
from modern_ui_styles import ModernUIStyles
from modern_ui_components import (
    ModernCard, ModernButton, ModernInput, ModernComboBox,
    ModernCheckBox, ModernSlider, StatusIndicator, GlassMorphismWidget
)

class ModernMainWindow(QWidget):
    """现代化主窗口类"""
    
    def __init__(self, original_window, parent=None):
        super().__init__(parent)
        self.original_window = original_window  # 保持对原窗口的引用
        self.setup_window()
        self.create_ui()
        self.setup_animations()
        self.connect_signals()
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle('OBS 去重软件 v2.0 - 现代化版本')
        self.setGeometry(100, 50, 1200, 900)
        self.setMinimumSize(1200, 900)
        
        # 设置窗口图标
        try:
            self.setWindowIcon(QIcon('obs2.ico'))
        except:
            pass
        
        # 应用现代化样式
        self.setStyleSheet(ModernUIStyles.get_main_window_style())
    
    def create_ui(self):
        """创建用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建顶部区域
        self.create_header(main_layout)
        
        # 创建主内容区域
        self.create_main_content(main_layout)
        
        # 创建底部区域
        self.create_footer(main_layout)
    
    def create_header(self, parent_layout):
        """创建顶部区域"""
        header_card = ModernCard()
        header_layout = QHBoxLayout()
        
        # 应用标题和图标
        title_layout = QVBoxLayout()
        
        app_title = QLabel("🎬 OBS 智能去重控制器")
        app_title.setStyleSheet(f"""
            QLabel {{
                font-size: 24pt;
                font-weight: bold;
                color: {ModernUIStyles.COLORS['primary']};
                margin: 0;
                padding: 0;
            }}
        """)
        
        app_subtitle = QLabel("现代化科技感界面 · 智能化去重方案")
        app_subtitle.setStyleSheet(f"""
            QLabel {{
                font-size: 12pt;
                color: {ModernUIStyles.COLORS['text_secondary']};
                margin-top: 5px;
            }}
        """)
        
        title_layout.addWidget(app_title)
        title_layout.addWidget(app_subtitle)
        header_layout.addLayout(title_layout)
        
        # 连接状态和控制按钮
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(15)
        
        # 状态指示器
        self.status_indicator = StatusIndicator("disconnected")
        controls_layout.addWidget(self.status_indicator)
        
        # 连接按钮
        self.connect_btn = ModernButton("🔗 连接到 OBS", "primary")
        self.connect_btn.clicked.connect(self.on_connect_clicked)
        controls_layout.addWidget(self.connect_btn)
        
        # 一键启动按钮
        self.quick_start_btn = ModernButton("🚀 一键启动", "success")
        self.quick_start_btn.clicked.connect(self.on_quick_start_clicked)
        self.quick_start_btn.setEnabled(False)
        controls_layout.addWidget(self.quick_start_btn)
        
        header_layout.addStretch()
        header_layout.addLayout(controls_layout)
        
        header_card.add_widget(QWidget())
        header_card.layout().addLayout(header_layout)
        parent_layout.addWidget(header_card)
    
    def create_main_content(self, parent_layout):
        """创建主内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background: {ModernUIStyles.COLORS['border']};
                width: 2px;
            }}
            QSplitter::handle:hover {{
                background: {ModernUIStyles.COLORS['primary']};
            }}
        """)
        
        # 左侧：功能配置区域
        self.create_config_panel(splitter)
        
        # 右侧：主要功能区域
        self.create_function_tabs(splitter)
        
        # 设置分割比例
        splitter.setSizes([350, 850])
        parent_layout.addWidget(splitter)
    
    def create_config_panel(self, parent):
        """创建配置面板"""
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setContentsMargins(0, 0, 10, 0)
        config_layout.setSpacing(15)
        
        # 配置面板标题
        config_title = QLabel("⚙️ 智能配置面板")
        config_title.setStyleSheet(f"""
            QLabel {{
                font-size: 16pt;
                font-weight: bold;
                color: {ModernUIStyles.COLORS['text_primary']};
                padding: 15px;
                background: linear-gradient(135deg, {ModernUIStyles.COLORS['surface']} 0%, #f1f5f9 100%);
                border-radius: 12px;
                border: 1px solid {ModernUIStyles.COLORS['border']};
            }}
        """)
        config_title.setAlignment(Qt.AlignCenter)
        config_layout.addWidget(config_title)
        
        # 媒体源选择卡片
        media_card = ModernCard("📺 媒体源配置")
        media_layout = QVBoxLayout()
        
        # 视频媒体源
        video_label = QLabel("视频媒体源:")
        video_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_primary']};")
        self.video_source_combo = ModernComboBox()
        
        media_layout.addWidget(video_label)
        media_layout.addWidget(self.video_source_combo)
        
        # 音频媒体源
        audio_label = QLabel("音频媒体源:")
        audio_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_primary']};")
        self.audio_source_combo = ModernComboBox()
        
        media_layout.addWidget(audio_label)
        media_layout.addWidget(self.audio_source_combo)
        
        # 刷新按钮
        refresh_btn = ModernButton("🔄 刷新媒体源", "info")
        refresh_btn.clicked.connect(self.on_refresh_sources)
        media_layout.addWidget(refresh_btn)
        
        media_card.add_widget(QWidget())
        media_card.layout().addLayout(media_layout)
        config_layout.addWidget(media_card)
        
        # 快速设置卡片
        quick_settings_card = ModernCard("⚡ 快速设置")
        quick_layout = QVBoxLayout()
        
        # 预设方案
        preset_label = QLabel("预设方案:")
        preset_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_primary']};")
        self.preset_combo = ModernComboBox()
        self.preset_combo.addItems([
            "🎯 轻度去重", "🔥 中度去重", "💪 强力去重", "🚀 极限去重", "🎨 自定义"
        ])
        
        quick_layout.addWidget(preset_label)
        quick_layout.addWidget(self.preset_combo)
        
        # 应用预设按钮
        apply_preset_btn = ModernButton("✨ 应用预设", "warning")
        apply_preset_btn.clicked.connect(self.on_apply_preset)
        quick_layout.addWidget(apply_preset_btn)
        
        quick_settings_card.add_widget(QWidget())
        quick_settings_card.layout().addLayout(quick_layout)
        config_layout.addWidget(quick_settings_card)
        
        # 系统状态卡片
        status_card = ModernCard("📊 系统状态")
        status_layout = QVBoxLayout()
        
        # CPU使用率
        cpu_label = QLabel("CPU 使用率: 0%")
        cpu_label.setStyleSheet(f"color: {ModernUIStyles.COLORS['text_secondary']};")
        status_layout.addWidget(cpu_label)
        
        # 内存使用率
        memory_label = QLabel("内存使用率: 0%")
        memory_label.setStyleSheet(f"color: {ModernUIStyles.COLORS['text_secondary']};")
        status_layout.addWidget(memory_label)
        
        # 活跃功能数
        active_label = QLabel("活跃功能: 0")
        active_label.setStyleSheet(f"color: {ModernUIStyles.COLORS['text_secondary']};")
        status_layout.addWidget(active_label)
        
        status_card.add_widget(QWidget())
        status_card.layout().addLayout(status_layout)
        config_layout.addWidget(status_card)
        
        config_layout.addStretch()
        parent.addWidget(config_widget)
    
    def create_function_tabs(self, parent):
        """创建功能标签页"""
        # 创建现代化标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(ModernUIStyles.get_modern_tab_style())
        
        # 视频去重标签页
        self.create_video_tab()
        
        # 音频去重标签页
        self.create_audio_tab()
        
        # 高级功能标签页
        self.create_advanced_tab()
        
        # 说明文档标签页
        self.create_help_tab()
        
        parent.addWidget(self.tab_widget)
    
    def create_video_tab(self):
        """创建视频去重标签页"""
        video_widget = QWidget()
        video_layout = QVBoxLayout(video_widget)
        video_layout.setSpacing(20)
        
        # 标题
        title = QLabel("🎬 视频去重功能")
        title.setStyleSheet(f"""
            QLabel {{
                font-size: 18pt;
                font-weight: bold;
                color: {ModernUIStyles.COLORS['primary']};
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid {ModernUIStyles.COLORS['border']};
            }}
        """)
        title.setAlignment(Qt.AlignCenter)
        video_layout.addWidget(title)
        
        # 功能卡片容器
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(20)
        
        # 智能加减速卡片
        speed_card = self.create_speed_control_card()
        cards_layout.addWidget(speed_card)
        
        # 模糊去重卡片
        blur_card = self.create_blur_control_card()
        cards_layout.addWidget(blur_card)
        
        video_layout.addLayout(cards_layout)
        
        # 第二行卡片
        cards_layout2 = QHBoxLayout()
        cards_layout2.setSpacing(20)
        
        # 移动去重卡片
        transform_card = self.create_transform_control_card()
        cards_layout2.addWidget(transform_card)
        
        # 颜色去重卡片
        color_card = self.create_color_control_card()
        cards_layout2.addWidget(color_card)
        
        video_layout.addLayout(cards_layout2)
        video_layout.addStretch()
        
        self.tab_widget.addTab(video_widget, "🎬 视频去重")
    
    def create_speed_control_card(self):
        """创建智能加减速控制卡片"""
        card = ModernCard("🚀 智能加减速")
        layout = QVBoxLayout()
        
        # 启用开关
        self.speed_checkbox = ModernCheckBox("启用智能加减速")
        layout.addWidget(self.speed_checkbox)
        
        # 速度范围设置
        speed_range_layout = QHBoxLayout()
        
        min_speed_layout = QVBoxLayout()
        min_label = QLabel("最低速度 (%)")
        min_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.speed_min_slider = ModernSlider()
        self.speed_min_slider.setRange(1, 200)
        self.speed_min_slider.setValue(90)
        min_speed_layout.addWidget(min_label)
        min_speed_layout.addWidget(self.speed_min_slider)
        
        max_speed_layout = QVBoxLayout()
        max_label = QLabel("最高速度 (%)")
        max_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.speed_max_slider = ModernSlider()
        self.speed_max_slider.setRange(1, 200)
        self.speed_max_slider.setValue(120)
        max_speed_layout.addWidget(max_label)
        max_speed_layout.addWidget(self.speed_max_slider)
        
        speed_range_layout.addLayout(min_speed_layout)
        speed_range_layout.addLayout(max_speed_layout)
        
        layout.addLayout(speed_range_layout)
        
        card.add_widget(QWidget())
        card.layout().addLayout(layout)
        return card

    def create_blur_control_card(self):
        """创建模糊去重控制卡片"""
        card = ModernCard("🌫️ 模糊去重")
        layout = QVBoxLayout()

        # 启用开关
        self.blur_checkbox = ModernCheckBox("启用模糊去重")
        layout.addWidget(self.blur_checkbox)

        # 警告提示
        warning_label = QLabel("⚠️ 注意：如果出现黑屏情况，请打开对应媒体源滤镜即可正常")
        warning_label.setStyleSheet(f"""
            QLabel {{
                color: {ModernUIStyles.COLORS['error']};
                font-size: 10pt;
                background: #fef2f2;
                padding: 8px;
                border-radius: 6px;
                border-left: 4px solid {ModernUIStyles.COLORS['error']};
            }}
        """)
        warning_label.setWordWrap(True)
        layout.addWidget(warning_label)

        # 滤镜名称
        filter_label = QLabel("模糊滤镜名称:")
        filter_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.blur_filter_input = ModernInput("Composite Blur")
        layout.addWidget(filter_label)
        layout.addWidget(self.blur_filter_input)

        # 半径范围
        radius_layout = QHBoxLayout()

        min_radius_layout = QVBoxLayout()
        min_radius_label = QLabel("最小半径")
        min_radius_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.blur_min_slider = ModernSlider()
        self.blur_min_slider.setRange(0, 50)
        self.blur_min_slider.setValue(0)
        min_radius_layout.addWidget(min_radius_label)
        min_radius_layout.addWidget(self.blur_min_slider)

        max_radius_layout = QVBoxLayout()
        max_radius_label = QLabel("最大半径")
        max_radius_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.blur_max_slider = ModernSlider()
        self.blur_max_slider.setRange(0, 50)
        self.blur_max_slider.setValue(20)
        max_radius_layout.addWidget(max_radius_label)
        max_radius_layout.addWidget(self.blur_max_slider)

        radius_layout.addLayout(min_radius_layout)
        radius_layout.addLayout(max_radius_layout)

        layout.addLayout(radius_layout)

        card.add_widget(QWidget())
        card.layout().addLayout(layout)
        return card

    def create_transform_control_card(self):
        """创建移动去重控制卡片"""
        card = ModernCard("↔️ 移动去重")
        layout = QVBoxLayout()

        # 启用开关
        self.transform_checkbox = ModernCheckBox("启用视频移动去重")
        layout.addWidget(self.transform_checkbox)

        # 缩放比例
        scale_label = QLabel("缩放比例 (%):")
        scale_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.transform_scale_slider = ModernSlider()
        self.transform_scale_slider.setRange(100, 200)
        self.transform_scale_slider.setValue(110)
        layout.addWidget(scale_label)
        layout.addWidget(self.transform_scale_slider)

        # 移动间隔
        interval_label = QLabel("移动间隔 (秒):")
        interval_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.transform_interval_slider = ModernSlider()
        self.transform_interval_slider.setRange(5, 1200)  # 0.5-120秒，以0.1秒为单位
        self.transform_interval_slider.setValue(20)  # 2秒
        layout.addWidget(interval_label)
        layout.addWidget(self.transform_interval_slider)

        card.add_widget(QWidget())
        card.layout().addLayout(layout)
        return card

    def create_color_control_card(self):
        """创建颜色去重控制卡片"""
        card = ModernCard("🎨 颜色去重")
        layout = QVBoxLayout()

        # 启用开关
        self.color_checkbox = ModernCheckBox("启用颜色去重")
        layout.addWidget(self.color_checkbox)

        # 滤镜名称
        filter_label = QLabel("颜色滤镜名称:")
        filter_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.color_filter_input = ModernInput("自动颜色校正")
        layout.addWidget(filter_label)
        layout.addWidget(self.color_filter_input)

        # 变化间隔
        interval_label = QLabel("变化间隔 (毫秒):")
        interval_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.color_interval_slider = ModernSlider()
        self.color_interval_slider.setRange(100, 10000)
        self.color_interval_slider.setValue(1500)
        layout.addWidget(interval_label)
        layout.addWidget(self.color_interval_slider)

        card.add_widget(QWidget())
        card.layout().addLayout(layout)
        return card

    def create_audio_tab(self):
        """创建音频去重标签页"""
        audio_widget = QWidget()
        audio_layout = QVBoxLayout(audio_widget)
        audio_layout.setSpacing(20)

        # 标题
        title = QLabel("🎵 音频去重功能")
        title.setStyleSheet(f"""
            QLabel {{
                font-size: 18pt;
                font-weight: bold;
                color: {ModernUIStyles.COLORS['primary']};
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid {ModernUIStyles.COLORS['border']};
            }}
        """)
        title.setAlignment(Qt.AlignCenter)
        audio_layout.addWidget(title)

        # 功能卡片容器
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(20)

        # 音频EQ去重卡片
        eq_card = self.create_audio_eq_card()
        cards_layout.addWidget(eq_card)

        # 断音控制卡片
        mute_card = self.create_audio_mute_card()
        cards_layout.addWidget(mute_card)

        audio_layout.addLayout(cards_layout)

        # 第二行卡片
        cards_layout2 = QHBoxLayout()
        cards_layout2.setSpacing(20)

        # 音量控制卡片
        volume_card = self.create_audio_volume_card()
        cards_layout2.addWidget(volume_card)

        # 音频播放器卡片
        player_card = self.create_audio_player_card()
        cards_layout2.addWidget(player_card)

        audio_layout.addLayout(cards_layout2)
        audio_layout.addStretch()

        self.tab_widget.addTab(audio_widget, "🎵 音频去重")

    def create_audio_eq_card(self):
        """创建音频EQ去重卡片"""
        card = ModernCard("🔊 音频EQ去重")
        layout = QVBoxLayout()

        # 启用开关
        self.audio_eq_checkbox = ModernCheckBox("启用音频EQ去重")
        layout.addWidget(self.audio_eq_checkbox)

        # EQ滤镜名称
        filter_label = QLabel("EQ滤镜名称:")
        filter_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.audio_eq_filter_input = ModernInput("3段式均衡器")
        layout.addWidget(filter_label)
        layout.addWidget(self.audio_eq_filter_input)

        # 频段控制
        freq_layout = QVBoxLayout()

        # 低频
        low_label = QLabel("低频增益 (dB):")
        low_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.audio_low_slider = ModernSlider()
        self.audio_low_slider.setRange(-20, 20)
        self.audio_low_slider.setValue(0)
        freq_layout.addWidget(low_label)
        freq_layout.addWidget(self.audio_low_slider)

        # 中频
        mid_label = QLabel("中频增益 (dB):")
        mid_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.audio_mid_slider = ModernSlider()
        self.audio_mid_slider.setRange(-20, 20)
        self.audio_mid_slider.setValue(0)
        freq_layout.addWidget(mid_label)
        freq_layout.addWidget(self.audio_mid_slider)

        # 高频
        high_label = QLabel("高频增益 (dB):")
        high_label.setStyleSheet(f"font-weight: 600; color: {ModernUIStyles.COLORS['text_secondary']};")
        self.audio_high_slider = ModernSlider()
        self.audio_high_slider.setRange(-20, 20)
        self.audio_high_slider.setValue(0)
        freq_layout.addWidget(high_label)
        freq_layout.addWidget(self.audio_high_slider)

        layout.addLayout(freq_layout)

        card.add_widget(QWidget())
        card.layout().addLayout(layout)
        return card
