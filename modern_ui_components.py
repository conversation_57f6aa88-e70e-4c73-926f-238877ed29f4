# -*- coding: utf-8 -*-
"""
现代化UI组件库
包含各种现代化的UI组件，如卡片、按钮、输入框等
"""

from PyQt5.QtWidgets import (
    QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout, 
    QFrame, QGraphicsDropShadowEffect, QScrollArea, QGroupBox,
    QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox,
    QSlider, QProgressBar, QTextEdit
)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, pyqtProperty, QRect
from PyQt5.QtGui import QPainter, QColor, QLinearGradient, QBrush, QPen, QFont
from modern_ui_styles import ModernUIStyles

class ModernCard(QFrame):
    """现代化卡片组件"""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.title = title
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet(f"""
            ModernCard {{
                background: {ModernUIStyles.COLORS['surface']};
                border-radius: 16px;
                border: 1px solid {ModernUIStyles.COLORS['border']};
                margin: 8px;
            }}
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
        
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(15)
        
        # 标题
        if self.title:
            self.title_label = QLabel(self.title)
            self.title_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 14pt;
                    font-weight: bold;
                    color: {ModernUIStyles.COLORS['text_primary']};
                    padding-bottom: 10px;
                    border-bottom: 2px solid {ModernUIStyles.COLORS['border']};
                }}
            """)
            self.main_layout.addWidget(self.title_label)
    
    def setup_animations(self):
        """设置动画"""
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        # 添加悬浮效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(102, 126, 234, 60))
        shadow.setOffset(0, 8)
        self.setGraphicsEffect(shadow)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        # 恢复原始阴影
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
        super().leaveEvent(event)
    
    def add_widget(self, widget):
        """添加组件到卡片"""
        self.main_layout.addWidget(widget)

class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    def __init__(self, text="", button_type="primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setup_style()
        self.setup_animations()
    
    def setup_style(self):
        """设置样式"""
        if self.button_type == "primary":
            color_start = ModernUIStyles.COLORS['primary']
            color_end = ModernUIStyles.COLORS['secondary']
        elif self.button_type == "success":
            color_start = ModernUIStyles.COLORS['success']
            color_end = "#059669"
        elif self.button_type == "warning":
            color_start = ModernUIStyles.COLORS['warning']
            color_end = "#d97706"
        elif self.button_type == "error":
            color_start = ModernUIStyles.COLORS['error']
            color_end = "#dc2626"
        else:
            color_start = "#6b7280"
            color_end = "#4b5563"
        
        self.setStyleSheet(f"""
            ModernButton {{
                background: linear-gradient(135deg, {color_start} 0%, {color_end} 100%);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 11pt;
                min-height: 20px;
            }}
            
            ModernButton:hover {{
                background: linear-gradient(135deg, {color_start}dd 0%, {color_end}dd 100%);
                transform: translateY(-2px);
            }}
            
            ModernButton:pressed {{
                background: linear-gradient(135deg, {color_start}bb 0%, {color_end}bb 100%);
                transform: translateY(0px);
            }}
            
            ModernButton:disabled {{
                background: #9ca3af;
                color: #6b7280;
            }}
        """)
    
    def setup_animations(self):
        """设置动画"""
        self.press_animation = QPropertyAnimation(self, b"geometry")
        self.press_animation.setDuration(100)
        self.press_animation.setEasingCurve(QEasingCurve.OutCubic)

class ModernInput(QLineEdit):
    """现代化输入框组件"""
    
    def __init__(self, placeholder="", parent=None):
        super().__init__(parent)
        self.setPlaceholderText(placeholder)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            ModernInput {{
                background: {ModernUIStyles.COLORS['surface']};
                border: 2px solid {ModernUIStyles.COLORS['border']};
                border-radius: 10px;
                padding: 12px 16px;
                font-size: 11pt;
                color: {ModernUIStyles.COLORS['text_primary']};
                selection-background-color: {ModernUIStyles.COLORS['primary']};
            }}
            
            ModernInput:focus {{
                border-color: {ModernUIStyles.COLORS['primary']};
                background: {ModernUIStyles.COLORS['surface']};
            }}
            
            ModernInput:hover {{
                border-color: #cbd5e1;
            }}
        """)

class ModernComboBox(QComboBox):
    """现代化下拉框组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            ModernComboBox {{
                background: {ModernUIStyles.COLORS['surface']};
                border: 2px solid {ModernUIStyles.COLORS['border']};
                border-radius: 10px;
                padding: 10px 15px;
                font-size: 11pt;
                color: {ModernUIStyles.COLORS['text_primary']};
                min-height: 20px;
            }}
            
            ModernComboBox:focus {{
                border-color: {ModernUIStyles.COLORS['primary']};
            }}
            
            ModernComboBox:hover {{
                border-color: #cbd5e1;
            }}
            
            ModernComboBox::drop-down {{
                border: none;
                width: 30px;
            }}
            
            ModernComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {ModernUIStyles.COLORS['text_secondary']};
                margin-right: 10px;
            }}
            
            ModernComboBox QAbstractItemView {{
                background: {ModernUIStyles.COLORS['surface']};
                border: 1px solid {ModernUIStyles.COLORS['border']};
                border-radius: 8px;
                selection-background-color: {ModernUIStyles.COLORS['primary']};
                selection-color: white;
                padding: 5px;
            }}
        """)

class ModernCheckBox(QCheckBox):
    """现代化复选框组件"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            ModernCheckBox {{
                font-weight: 600;
                color: {ModernUIStyles.COLORS['text_primary']};
                spacing: 12px;
                padding: 8px;
            }}
            
            ModernCheckBox::indicator {{
                width: 22px;
                height: 22px;
                border-radius: 6px;
                border: 2px solid {ModernUIStyles.COLORS['border']};
                background: {ModernUIStyles.COLORS['surface']};
            }}
            
            ModernCheckBox::indicator:hover {{
                border-color: {ModernUIStyles.COLORS['primary']};
                background: rgba(102, 126, 234, 0.05);
            }}
            
            ModernCheckBox::indicator:checked {{
                background: linear-gradient(135deg, {ModernUIStyles.COLORS['success']} 0%, #059669 100%);
                border-color: {ModernUIStyles.COLORS['success']};
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }}
        """)

class ModernSlider(QSlider):
    """现代化滑块组件"""
    
    def __init__(self, orientation=Qt.Horizontal, parent=None):
        super().__init__(orientation, parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            ModernSlider::groove:horizontal {{
                border: none;
                height: 8px;
                background: linear-gradient(90deg, {ModernUIStyles.COLORS['border']} 0%, #cbd5e1 100%);
                border-radius: 4px;
            }}
            
            ModernSlider::handle:horizontal {{
                background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
                border: 3px solid white;
                width: 24px;
                height: 24px;
                margin: -10px 0;
                border-radius: 15px;
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            }}
            
            ModernSlider::handle:horizontal:hover {{
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.5);
            }}
            
            ModernSlider::sub-page:horizontal {{
                background: linear-gradient(90deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
                border-radius: 4px;
            }}
        """)

class ModernProgressBar(QProgressBar):
    """现代化进度条组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            ModernProgressBar {{
                border: none;
                border-radius: 8px;
                background: {ModernUIStyles.COLORS['border']};
                text-align: center;
                font-weight: 600;
                color: {ModernUIStyles.COLORS['text_primary']};
            }}
            
            ModernProgressBar::chunk {{
                background: linear-gradient(90deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
                border-radius: 8px;
            }}
        """)

class GlassMorphismWidget(QWidget):
    """玻璃拟态组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            GlassMorphismWidget {{
                background: rgba(255, 255, 255, 0.25);
                border: 1px solid rgba(255, 255, 255, 0.18);
                border-radius: 16px;
            }}
        """)
        
        # 添加模糊效果（注意：PyQt5中backdrop-filter支持有限）
        self.setAttribute(Qt.WA_TranslucentBackground)

class StatusIndicator(QLabel):
    """状态指示器组件"""
    
    def __init__(self, status="disconnected", parent=None):
        super().__init__(parent)
        self.status = status
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        if self.status == "connected":
            bg_color = f"linear-gradient(135deg, {ModernUIStyles.COLORS['success']} 0%, #059669 100%)"
            text = "🟢 已连接"
        else:
            bg_color = f"linear-gradient(135deg, {ModernUIStyles.COLORS['error']} 0%, #dc2626 100%)"
            text = "🔴 未连接"
        
        self.setText(text)
        self.setStyleSheet(f"""
            StatusIndicator {{
                background: {bg_color};
                color: white;
                border-radius: 20px;
                padding: 8px 16px;
                font-weight: 600;
                font-size: 11pt;
            }}
        """)
        self.setAlignment(Qt.AlignCenter)
    
    def set_status(self, status):
        """设置状态"""
        self.status = status
        self.setup_style()
