# -*- coding: utf-8 -*-
"""
现代化OBS控制器启动脚本
整合原有功能与现代化UI界面
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QSplashScreen, QLabel
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont, QIcon
from modern_main_window import ModernMainWindow
from modern_ui_styles import ModernUIStyles

# 导入原有的主模块
try:
    from main_module import MainWindow as OriginalMainWindow
except ImportError:
    print("警告: 无法导入原始主模块，某些功能可能不可用")
    OriginalMainWindow = None

class ModernSplashScreen(QSplashScreen):
    """现代化启动画面"""
    
    def __init__(self):
        # 创建启动画面
        pixmap = QPixmap(400, 300)
        pixmap.fill(Qt.transparent)
        super().__init__(pixmap)
        
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 创建标签
        self.title_label = QLabel("🎬 OBS 智能去重控制器", self)
        self.title_label.setGeometry(50, 100, 300, 50)
        self.title_label.setAlignment(Qt.AlignCenter)
        
        self.subtitle_label = QLabel("现代化科技感界面 · 正在启动...", self)
        self.subtitle_label.setGeometry(50, 150, 300, 30)
        self.subtitle_label.setAlignment(Qt.AlignCenter)
        
        self.version_label = QLabel("v2.0 - Modern UI Edition", self)
        self.version_label.setGeometry(50, 200, 300, 30)
        self.version_label.setAlignment(Qt.AlignCenter)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet(f"""
            QSplashScreen {{
                background: linear-gradient(135deg, {ModernUIStyles.COLORS['background']} 0%, #e2e8f0 100%);
                border-radius: 20px;
                border: 2px solid {ModernUIStyles.COLORS['border']};
            }}
        """)
        
        self.title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 20pt;
                font-weight: bold;
                color: {ModernUIStyles.COLORS['primary']};
                background: transparent;
            }}
        """)
        
        self.subtitle_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12pt;
                color: {ModernUIStyles.COLORS['text_secondary']};
                background: transparent;
            }}
        """)
        
        self.version_label.setStyleSheet(f"""
            QLabel {{
                font-size: 10pt;
                color: {ModernUIStyles.COLORS['text_secondary']};
                background: transparent;
                font-style: italic;
            }}
        """)

def setup_application():
    """设置应用程序"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("OBS 智能去重控制器")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Modern UI Studio")
    
    # 设置应用程序图标
    try:
        app.setWindowIcon(QIcon('obs2.ico'))
    except:
        pass
    
    # 设置全局字体
    font = QFont("Inter", 10)
    font.setStyleHint(QFont.SansSerif)
    app.setFont(font)
    
    # 应用全局样式
    app.setStyleSheet(f"""
        * {{
            font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
        }}
        
        QApplication {{
            background: {ModernUIStyles.COLORS['background']};
        }}
        
        QToolTip {{
            background: {ModernUIStyles.COLORS['surface']};
            color: {ModernUIStyles.COLORS['text_primary']};
            border: 1px solid {ModernUIStyles.COLORS['border']};
            border-radius: 6px;
            padding: 8px;
            font-size: 10pt;
        }}
        
        QMessageBox {{
            background: {ModernUIStyles.COLORS['surface']};
            color: {ModernUIStyles.COLORS['text_primary']};
        }}
        
        QMessageBox QPushButton {{
            background: linear-gradient(135deg, {ModernUIStyles.COLORS['primary']} 0%, {ModernUIStyles.COLORS['secondary']} 100%);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 600;
            min-width: 80px;
        }}
        
        QMessageBox QPushButton:hover {{
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }}
    """)
    
    return app

def show_splash_screen():
    """显示启动画面"""
    splash = ModernSplashScreen()
    splash.show()
    
    # 模拟加载过程
    messages = [
        "正在初始化现代化UI组件...",
        "正在加载样式主题...",
        "正在连接原始功能模块...",
        "正在设置动画效果...",
        "启动完成！"
    ]
    
    for i, message in enumerate(messages):
        splash.subtitle_label.setText(message)
        QApplication.processEvents()
        
        # 使用QTimer来模拟加载延迟
        timer = QTimer()
        timer.setSingleShot(True)
        timer.timeout.connect(lambda: None)
        timer.start(500)
        
        # 等待定时器完成
        while timer.isActive():
            QApplication.processEvents()
    
    return splash

def create_main_window():
    """创建主窗口"""
    # 首先尝试创建原始窗口（隐藏）
    original_window = None
    if OriginalMainWindow:
        try:
            original_window = OriginalMainWindow()
            original_window.hide()  # 隐藏原始窗口
            print("✅ 成功创建原始功能窗口")
        except Exception as e:
            print(f"⚠️ 创建原始窗口时出错: {e}")
    
    # 创建现代化窗口
    modern_window = ModernMainWindow(original_window)
    
    return modern_window, original_window

def main():
    """主函数"""
    print("🚀 启动现代化OBS控制器...")
    
    # 设置应用程序
    app = setup_application()
    
    # 显示启动画面
    print("📱 显示启动画面...")
    splash = show_splash_screen()
    
    try:
        # 创建主窗口
        print("🏗️ 创建主窗口...")
        modern_window, original_window = create_main_window()
        
        # 关闭启动画面
        splash.finish(modern_window)
        
        # 显示现代化窗口
        modern_window.show()
        
        print("✨ 现代化UI启动成功！")
        print("🎯 功能特色:")
        print("   • 科技感浅色主题")
        print("   • Neumorphism新拟态设计")
        print("   • 渐变与光效")
        print("   • 玻璃拟态效果")
        print("   • 高级动画效果")
        print("   • 智能状态指示")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 如果现代化UI启动失败，尝试启动原始UI
        if OriginalMainWindow:
            print("🔄 尝试启动原始UI...")
            try:
                original_window = OriginalMainWindow()
                original_window.show()
                sys.exit(app.exec_())
            except Exception as e2:
                print(f"❌ 原始UI也启动失败: {e2}")
        
        sys.exit(1)

if __name__ == "__main__":
    main()
