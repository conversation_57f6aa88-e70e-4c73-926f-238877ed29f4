# -*- coding: utf-8 -*-
"""
现代化UI配置管理
管理主题、动画、布局等配置选项
"""

import json
import os
from PyQt5.QtCore import QSettings

class ModernUIConfig:
    """现代化UI配置管理类"""
    
    def __init__(self):
        self.config_file = "modern_ui_config.json"
        self.settings = QSettings("ModernUI", "OBSController")
        self.default_config = self.get_default_config()
        self.current_config = self.load_config()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "theme": {
                "name": "科技感浅色主题",
                "primary_color": "#667eea",
                "secondary_color": "#764ba2",
                "background_color": "#f8fafc",
                "surface_color": "#ffffff",
                "text_primary": "#1e293b",
                "text_secondary": "#64748b",
                "success_color": "#10b981",
                "warning_color": "#f59e0b",
                "error_color": "#ef4444",
                "info_color": "#3b82f6",
                "border_color": "#e2e8f0"
            },
            "animations": {
                "enabled": True,
                "duration": 300,
                "easing": "OutCubic",
                "hover_effects": True,
                "fade_in_on_startup": True,
                "smooth_transitions": True
            },
            "layout": {
                "window_width": 1200,
                "window_height": 900,
                "sidebar_width": 350,
                "card_spacing": 20,
                "card_padding": 20,
                "border_radius": 16
            },
            "effects": {
                "neumorphism": True,
                "glass_morphism": True,
                "shadows": True,
                "gradients": True,
                "blur_effects": True,
                "glow_effects": True
            },
            "fonts": {
                "primary_font": "Inter",
                "secondary_font": "Noto Sans SC",
                "fallback_font": "Microsoft YaHei",
                "base_size": 11,
                "title_size": 16,
                "header_size": 20
            },
            "performance": {
                "hardware_acceleration": True,
                "smooth_scrolling": True,
                "reduce_animations": False,
                "low_power_mode": False
            },
            "accessibility": {
                "high_contrast": False,
                "large_text": False,
                "reduce_motion": False,
                "screen_reader_support": False
            },
            "presets": {
                "light_deduplication": {
                    "speed_min": 95,
                    "speed_max": 105,
                    "blur_min": 0,
                    "blur_max": 5,
                    "transform_scale": 105,
                    "color_interval": 3000
                },
                "medium_deduplication": {
                    "speed_min": 90,
                    "speed_max": 115,
                    "blur_min": 0,
                    "blur_max": 10,
                    "transform_scale": 110,
                    "color_interval": 2000
                },
                "strong_deduplication": {
                    "speed_min": 85,
                    "speed_max": 125,
                    "blur_min": 0,
                    "blur_max": 20,
                    "transform_scale": 120,
                    "color_interval": 1500
                },
                "extreme_deduplication": {
                    "speed_min": 80,
                    "speed_max": 140,
                    "blur_min": 0,
                    "blur_max": 30,
                    "transform_scale": 130,
                    "color_interval": 1000
                }
            }
        }
    
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置，确保所有键都存在
                    return self.merge_configs(self.default_config, config)
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config.copy()
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def merge_configs(self, default, user):
        """合并配置，确保用户配置包含所有默认键"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, key_path, default=None):
        """获取配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        value = self.current_config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path, value):
        """设置配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        config = self.current_config
        
        # 导航到最后一级的父级
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.current_config = self.default_config.copy()
        return self.save_config()
    
    def get_theme_colors(self):
        """获取主题颜色"""
        return self.get('theme', {})
    
    def get_animation_settings(self):
        """获取动画设置"""
        return self.get('animations', {})
    
    def get_layout_settings(self):
        """获取布局设置"""
        return self.get('layout', {})
    
    def get_effect_settings(self):
        """获取效果设置"""
        return self.get('effects', {})
    
    def get_font_settings(self):
        """获取字体设置"""
        return self.get('fonts', {})
    
    def get_preset(self, preset_name):
        """获取预设配置"""
        return self.get(f'presets.{preset_name}', {})
    
    def set_preset(self, preset_name, preset_config):
        """设置预设配置"""
        self.set(f'presets.{preset_name}', preset_config)
        return self.save_config()
    
    def get_all_presets(self):
        """获取所有预设"""
        return self.get('presets', {})
    
    def export_config(self, file_path):
        """导出配置到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path):
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
                self.current_config = self.merge_configs(self.default_config, imported_config)
                return self.save_config()
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
    
    def validate_config(self):
        """验证配置的有效性"""
        errors = []
        
        # 验证主题颜色格式
        theme = self.get('theme', {})
        color_keys = ['primary_color', 'secondary_color', 'background_color', 'surface_color']
        for key in color_keys:
            color = theme.get(key, '')
            if not color.startswith('#') or len(color) != 7:
                errors.append(f"无效的颜色格式: {key} = {color}")
        
        # 验证数值范围
        layout = self.get('layout', {})
        if layout.get('window_width', 0) < 800:
            errors.append("窗口宽度不能小于800像素")
        if layout.get('window_height', 0) < 600:
            errors.append("窗口高度不能小于600像素")
        
        # 验证动画持续时间
        animations = self.get('animations', {})
        duration = animations.get('duration', 0)
        if duration < 0 or duration > 2000:
            errors.append("动画持续时间应在0-2000毫秒之间")
        
        return errors
    
    def get_config_summary(self):
        """获取配置摘要"""
        return {
            "主题": self.get('theme.name', '未知'),
            "动画": "启用" if self.get('animations.enabled', False) else "禁用",
            "特效": "启用" if self.get('effects.neumorphism', False) else "禁用",
            "窗口大小": f"{self.get('layout.window_width', 0)}x{self.get('layout.window_height', 0)}",
            "字体": self.get('fonts.primary_font', '默认'),
            "预设数量": len(self.get('presets', {}))
        }

# 全局配置实例
ui_config = ModernUIConfig()
